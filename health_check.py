#!/usr/bin/env python3
"""
Health check script for Kite Trading MCP Server
Monitors the health of both OAuth callback and MCP servers
"""

import requests
import json
import time
import sys
from datetime import datetime

def check_server_health(url, name, timeout=10):
    """Check if a server is healthy"""
    try:
        response = requests.get(url, timeout=timeout)
        if response.status_code == 200:
            print(f"✅ {name}: Healthy (Status: {response.status_code})")
            return True
        else:
            print(f"⚠️ {name}: Unhealthy (Status: {response.status_code})")
            return False
    except requests.exceptions.ConnectionError:
        print(f"❌ {name}: Connection refused")
        return False
    except requests.exceptions.Timeout:
        print(f"⏰ {name}: Timeout after {timeout}s")
        return False
    except Exception as e:
        print(f"💥 {name}: Error - {e}")
        return False

def check_mcp_functionality(base_url, timeout=10):
    """Test MCP server functionality"""
    try:
        mcp_url = f"{base_url}/mcp"
        
        # Test MCP request
        test_request = {
            "jsonrpc": "2.0",
            "id": 1,
            "method": "tools/list"
        }
        
        response = requests.post(
            mcp_url, 
            json=test_request,
            headers={'Content-Type': 'application/json'},
            timeout=timeout
        )
        
        if response.status_code == 200:
            data = response.json()
            if 'result' in data and 'tools' in data['result']:
                tool_count = len(data['result']['tools'])
                print(f"✅ MCP Functionality: Working ({tool_count} tools available)")
                return True
            else:
                print(f"⚠️ MCP Functionality: Unexpected response format")
                return False
        else:
            print(f"❌ MCP Functionality: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"💥 MCP Functionality: Error - {e}")
        return False

def main():
    """Main health check function"""
    print(f"🏥 Kite Trading MCP Server Health Check")
    print(f"Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 50)
    
    # Get server URLs
    if len(sys.argv) > 1:
        base_url = sys.argv[1].rstrip('/')
    else:
        base_url = input("Enter your server base URL (e.g., https://your-domain.com or http://your-ip): ").strip().rstrip('/')
    
    if not base_url:
        print("❌ Server URL is required!")
        sys.exit(1)
    
    # Define health check URLs
    oauth_health_url = f"{base_url}:8080/health"
    mcp_health_url = f"{base_url}:3000/health"
    
    print(f"🔍 Checking servers at: {base_url}")
    print()
    
    # Check OAuth callback server
    oauth_healthy = check_server_health(oauth_health_url, "OAuth Callback Server (8080)")
    
    # Check MCP server
    mcp_healthy = check_server_health(mcp_health_url, "MCP Server (3000)")
    
    # Check MCP functionality
    mcp_functional = False
    if mcp_healthy:
        mcp_functional = check_mcp_functionality(f"{base_url}:3000")
    
    print()
    print("📊 Summary:")
    print(f"   OAuth Server: {'✅ Healthy' if oauth_healthy else '❌ Unhealthy'}")
    print(f"   MCP Server: {'✅ Healthy' if mcp_healthy else '❌ Unhealthy'}")
    print(f"   MCP Functionality: {'✅ Working' if mcp_functional else '❌ Not Working'}")
    
    # Overall status
    if oauth_healthy and mcp_healthy and mcp_functional:
        print(f"\n🎉 Overall Status: All systems operational!")
        sys.exit(0)
    elif oauth_healthy and mcp_healthy:
        print(f"\n⚠️ Overall Status: Servers running but MCP functionality issues")
        sys.exit(1)
    else:
        print(f"\n❌ Overall Status: System issues detected")
        sys.exit(2)

if __name__ == "__main__":
    main()
