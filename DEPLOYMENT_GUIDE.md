# 🚀 Complete Deployment Guide: Kite Trading MCP Server

This guide will help you deploy your Kite Trading MCP Server on a DigitalOcean droplet and connect it to Claude Desktop.

## 📋 Prerequisites

1. **DigitalOcean Droplet** (Ubuntu 20.04+ recommended, minimum 1GB RAM)
2. **Domain name** pointing to your droplet (or use droplet IP)
3. **Zerodha Kite Connect API credentials**
4. **Claude Desktop** installed on your local machine

## 🔧 Step 1: Setup Your Droplet

### 1.1 Create and Configure Droplet

```bash
# SSH into your droplet
ssh root@your-droplet-ip

# Update system
apt update && apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sh get-docker.sh

# Install Docker Compose
curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
chmod +x /usr/local/bin/docker-compose

# Verify installations
docker --version
docker-compose --version
```

### 1.2 Clone Your Repository

```bash
# Clone your repository (replace with your actual repo)
git clone https://github.com/yourusername/kite-trading.git
cd kite-trading

# Or upload files manually using scp
```

## 🔑 Step 2: Configure Environment

### 2.1 Create Environment File

```bash
# Copy the example environment file
cp .env.example .env

# Edit with your credentials
nano .env
```

### 2.2 Required Environment Variables

```bash
# Zerodha Kite Connect API Credentials
KITE_API_KEY=your_actual_api_key
KITE_API_SECRET=your_actual_api_secret

# OpenAI API Key (optional, for AI features)
OPENAI_API_KEY=your_openai_key

# Replace with your droplet's domain or IP
KITE_REDIRECT_URL=https://your-domain.com/callback
DROPLET_CALLBACK_URL=https://your-domain.com:8080

# Server configuration
MCP_SERVER_PORT=3000
CALLBACK_SERVER_PORT=8080
DOCKER_ENV=true
```

## 🐳 Step 3: Deploy with Docker

### 3.1 Make Deployment Script Executable

```bash
chmod +x deploy_to_droplet.sh
```

### 3.2 Run Deployment

```bash
# Deploy the application
./deploy_to_droplet.sh
```

### 3.3 Manual Deployment (Alternative)

```bash
# Create directories
mkdir -p data logs

# Build and start containers
docker-compose build --no-cache
docker-compose up -d

# Check status
docker-compose ps
docker-compose logs -f
```

## 🔗 Step 4: Configure Firewall and Ports

```bash
# Allow necessary ports
ufw allow 22    # SSH
ufw allow 80    # HTTP
ufw allow 443   # HTTPS
ufw allow 3000  # MCP Server
ufw allow 8080  # OAuth Callback

# Enable firewall
ufw --force enable
```

## 🌐 Step 5: Setup Domain/SSL (Recommended)

### Option A: Using Nginx Reverse Proxy

```bash
# Install Nginx
apt install nginx -y

# Create Nginx configuration
cat > /etc/nginx/sites-available/kite-trading << 'EOF'
server {
    listen 80;
    server_name your-domain.com;
    
    location / {
        proxy_pass http://localhost:3000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
    
    location /callback {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
EOF

# Enable site
ln -s /etc/nginx/sites-available/kite-trading /etc/nginx/sites-enabled/
nginx -t
systemctl restart nginx
```

### Option B: Direct Access (Simpler)

Just use your droplet IP with ports:
- MCP Server: `http://your-droplet-ip:3000`
- OAuth Callback: `http://your-droplet-ip:8080`

## 💻 Step 6: Configure Claude Desktop

### 6.1 Update Claude Desktop Configuration

Edit your Claude Desktop configuration file:

**Windows**: `%APPDATA%\Claude\claude_desktop_config.json`
**macOS**: `~/Library/Application Support/Claude/claude_desktop_config.json`

```json
{
  "mcpServers": {
    "zerodha-kite-trading": {
      "command": "python",
      "args": [
        "path/to/your/local/mcp_bridge.py"
      ],
      "env": {
        "DISABLE_SSL_VERIFICATION": "true"
      }
    }
  }
}
```

### 6.2 Update MCP Bridge Configuration

Edit `mcp_bridge.py` to point to your droplet:

```python
# Update the URL to your droplet
url = "https://your-domain.com:3000/mcp"
# or
url = "http://your-droplet-ip:3000/mcp"
```

## ✅ Step 7: Test Your Deployment

### 7.1 Test Server Health

```bash
# Test OAuth callback server
curl http://your-droplet-ip:8080/health

# Test MCP server
curl http://your-droplet-ip:3000/health

# Check Docker containers
docker-compose ps
```

### 7.2 Test Claude Desktop Integration

1. Restart Claude Desktop
2. Open a new conversation
3. Try: "Check authentication status"
4. If not authenticated, try: "Get me a login URL"

## 🔧 Step 8: Authentication Flow

### 8.1 First-Time Authentication

1. In Claude Desktop, ask: "Get me a login URL"
2. Click the provided URL
3. Log in to your Zerodha account
4. System will automatically capture the callback
5. Authentication complete!

### 8.2 Trading Commands

Once authenticated, you can use:
- "Buy 10 shares of RELIANCE"
- "Sell 5 shares of TCS"
- "Show my portfolio"
- "Check authentication status"

## 🛠️ Troubleshooting

### Common Issues

1. **Connection Refused**
   ```bash
   # Check if containers are running
   docker-compose ps
   
   # Check logs
   docker-compose logs -f
   ```

2. **Authentication Fails**
   ```bash
   # Check environment variables
   docker-compose exec kite-droplet-server env | grep KITE
   
   # Restart containers
   docker-compose restart
   ```

3. **SSL Certificate Issues**
   ```bash
   # For development, disable SSL verification
   export DISABLE_SSL_VERIFICATION=true
   ```

### Useful Commands

```bash
# View real-time logs
docker-compose logs -f

# Restart services
docker-compose restart

# Update deployment
git pull
docker-compose up --build -d

# Access container shell
docker-compose exec kite-droplet-server bash

# Check authentication status
docker-compose exec kite-droplet-server python auth_manager.py check
```

## 🔒 Security Considerations

1. **Use HTTPS in production**
2. **Secure your API keys**
3. **Regular security updates**
4. **Monitor access logs**
5. **Use proper SSL certificates**

## 📞 Support

If you encounter issues:
1. Check the logs: `docker-compose logs -f`
2. Verify environment variables
3. Test network connectivity
4. Check Zerodha API status

Your Kite Trading MCP Server should now be running successfully on your droplet and connected to Claude Desktop!
