#!/bin/bash

# Kite Trading MCP Server - DigitalOcean Droplet Deployment Script
# This script helps deploy your MCP server to a DigitalOcean droplet

set -e  # Exit on any error

echo "🚀 Kite Trading MCP Server - Droplet Deployment"
echo "================================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if .env file exists
if [ ! -f ".env" ]; then
    print_error ".env file not found!"
    print_status "Please create .env file from .env.example:"
    echo "  cp .env.example .env"
    echo "  # Edit .env with your actual credentials"
    exit 1
fi

# Load environment variables
source .env

# Validate required environment variables
if [ -z "$KITE_API_KEY" ] || [ -z "$KITE_API_SECRET" ]; then
    print_error "KITE_API_KEY and KITE_API_SECRET must be set in .env file"
    exit 1
fi

print_status "Environment variables loaded successfully"

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

print_status "Docker and Docker Compose are available"

# Create necessary directories
print_status "Creating necessary directories..."
mkdir -p data logs
chmod 755 data logs

# Build and start the containers
print_status "Building Docker containers..."
docker-compose build --no-cache

print_status "Starting services..."
docker-compose up -d

# Wait for services to start
print_status "Waiting for services to start..."
sleep 10

# Check service health
print_status "Checking service health..."

# Check OAuth callback server
if curl -f -s http://localhost:8080/health > /dev/null; then
    print_success "OAuth Callback Server (port 8080) is healthy"
else
    print_warning "OAuth Callback Server (port 8080) is not responding"
fi

# Check MCP server
if curl -f -s http://localhost:3000/health > /dev/null; then
    print_success "MCP Server (port 3000) is healthy"
else
    print_warning "MCP Server (port 3000) is not responding"
fi

# Display service status
print_status "Service Status:"
docker-compose ps

# Display logs
print_status "Recent logs:"
docker-compose logs --tail=20

echo ""
print_success "🎉 Deployment completed!"
echo ""
echo "📋 Next Steps:"
echo "1. Update your domain DNS to point to this droplet"
echo "2. Configure Claude Desktop with the MCP bridge"
echo "3. Test authentication by visiting: https://your-domain.com:8080/health"
echo ""
echo "🔗 Service URLs:"
echo "   OAuth Callback: https://your-domain.com:8080"
echo "   MCP Server: https://your-domain.com:3000"
echo ""
echo "📝 Useful Commands:"
echo "   View logs: docker-compose logs -f"
echo "   Restart: docker-compose restart"
echo "   Stop: docker-compose down"
echo "   Update: git pull && docker-compose up --build -d"
