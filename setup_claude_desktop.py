#!/usr/bin/env python3
"""
Setup script for Claude <PERSON> MCP configuration
Automatically configures <PERSON> to connect to your droplet
"""

import json
import os
import platform
import shutil
from pathlib import Path

def get_claude_config_path():
    """Get the Claude Desktop configuration file path based on OS"""
    system = platform.system()
    
    if system == "Windows":
        return Path(os.environ.get('APPDATA', '')) / 'Claude' / 'claude_desktop_config.json'
    elif system == "Darwin":  # macOS
        return Path.home() / 'Library' / 'Application Support' / 'Claude' / 'claude_desktop_config.json'
    elif system == "Linux":
        return Path.home() / '.config' / 'claude' / 'claude_desktop_config.json'
    else:
        raise Exception(f"Unsupported operating system: {system}")

def get_mcp_bridge_path():
    """Get the absolute path to mcp_bridge.py"""
    current_dir = Path(__file__).parent.absolute()
    return current_dir / 'mcp_bridge.py'

def create_claude_config(droplet_url, disable_ssl=True):
    """Create Claude Desktop configuration"""
    
    config_path = get_claude_config_path()
    mcp_bridge_path = get_mcp_bridge_path()
    
    # Create directory if it doesn't exist
    config_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Backup existing config if it exists
    if config_path.exists():
        backup_path = config_path.with_suffix('.json.backup')
        shutil.copy2(config_path, backup_path)
        print(f"✅ Backed up existing config to: {backup_path}")
    
    # Create new configuration
    config = {
        "mcpServers": {
            "zerodha-kite-trading": {
                "command": "python",
                "args": [str(mcp_bridge_path)],
                "env": {}
            }
        }
    }
    
    # Add SSL verification disable if requested
    if disable_ssl:
        config["mcpServers"]["zerodha-kite-trading"]["env"]["DISABLE_SSL_VERIFICATION"] = "true"
    
    # Write configuration
    with open(config_path, 'w') as f:
        json.dump(config, f, indent=2)
    
    return config_path

def update_mcp_bridge_url(droplet_url):
    """Update the MCP bridge to point to your droplet"""
    
    mcp_bridge_path = get_mcp_bridge_path()
    
    if not mcp_bridge_path.exists():
        print(f"❌ MCP bridge file not found: {mcp_bridge_path}")
        return False
    
    # Read current content
    with open(mcp_bridge_path, 'r') as f:
        content = f.read()
    
    # Update the URL
    old_url_pattern = 'url = "https://zap.zicuro.shop:3000/mcp"'
    new_url = f'url = "{droplet_url}/mcp"'
    
    if old_url_pattern in content:
        content = content.replace(old_url_pattern, new_url)
        
        # Write updated content
        with open(mcp_bridge_path, 'w') as f:
            f.write(content)
        
        print(f"✅ Updated MCP bridge URL to: {droplet_url}/mcp")
        return True
    else:
        print(f"⚠️ Could not find URL pattern in {mcp_bridge_path}")
        print(f"Please manually update the URL to: {droplet_url}/mcp")
        return False

def main():
    """Main setup function"""
    print("🚀 Claude Desktop MCP Configuration Setup")
    print("=" * 50)
    
    # Get droplet URL from user
    print("\n📝 Please provide your droplet information:")
    droplet_url = input("Enter your droplet URL (e.g., https://your-domain.com:3000 or http://your-ip:3000): ").strip()
    
    if not droplet_url:
        print("❌ Droplet URL is required!")
        return
    
    # Remove trailing slash if present
    droplet_url = droplet_url.rstrip('/')
    
    # Ask about SSL verification
    disable_ssl = input("Disable SSL verification? (y/N): ").strip().lower() in ['y', 'yes']
    
    try:
        # Update MCP bridge
        print(f"\n🔧 Updating MCP bridge configuration...")
        update_mcp_bridge_url(droplet_url)
        
        # Create Claude Desktop config
        print(f"\n🔧 Creating Claude Desktop configuration...")
        config_path = create_claude_config(droplet_url, disable_ssl)
        
        print(f"\n✅ Setup completed successfully!")
        print(f"📁 Configuration saved to: {config_path}")
        print(f"🔗 MCP Server URL: {droplet_url}/mcp")
        
        print(f"\n📋 Next steps:")
        print(f"1. Restart Claude Desktop")
        print(f"2. Open a new conversation")
        print(f"3. Test with: 'Check authentication status'")
        print(f"4. If not authenticated, try: 'Get me a login URL'")
        
        if disable_ssl:
            print(f"\n⚠️ SSL verification is disabled for development.")
            print(f"   For production, use proper SSL certificates.")
        
    except Exception as e:
        print(f"\n❌ Setup failed: {e}")
        print(f"Please check the error and try again.")

if __name__ == "__main__":
    main()
