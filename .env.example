# Kite Connect API Configuration
# Get these from your Zerodha Kite Connect app: https://kite.trade/

# Required: Your Kite Connect API Key
KITE_API_KEY=your_api_key_here

# Required: Your Kite Connect API Secret
KITE_API_SECRET=your_api_secret_here

# Required: Your Kite Connect redirect URL (must match your app settings)
KITE_REDIRECT_URL=https://zap.zicuro.shop/callback

# Required: Droplet callback server URL (for internal server communication)
DROPLET_CALLBACK_URL=http://zap.zicuro.shop:8080

# Optional: Local port for token capture (defaults to 8765)
LOCAL_PORT=8765

# Docker environment flag (set to true when running in Docker)
DOCKER_ENV=true
